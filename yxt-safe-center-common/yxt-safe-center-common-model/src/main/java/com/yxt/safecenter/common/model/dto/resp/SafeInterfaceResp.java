package com.yxt.safecenter.common.model.dto.resp;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.enums.EncryptionRequiredEnum;
import com.yxt.safecenter.common.model.enums.InterfaceStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@ApiModel
@Data
public class SafeInterfaceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键", example = "1", required = true)
    private Long id;

    @ApiModelProperty(value = "服务名", example = "UserService", required = true)
    private String applicationName;

    @ApiModelProperty(value = "API类", example = "com.example.UserController", required = true)
    private String apiClass;

    @ApiModelProperty(value = "API方法", example = "getUser", required = true)
    private String apiMethod;

    @ApiModelProperty(value = "请求方式", example = "GET", required = true)
    private String apiWay;

    @ApiModelProperty(value = "path路径", example = "/users/{id}", required = true)
    private String apiPath;

    @ApiModelProperty(value = "接口名称", example = "获取用户信息", required = true)
    private String apiName;

    /**
     * 请求参数信息
     */
    @ApiModelProperty(value = "请求参数信息", example = "{}", required = false)
    private String reqParamsInfo;

    /**
     * 响应参数信息
     */
    @ApiModelProperty(value = "响应参数信息", example = "{}", required = false)
    private String respParamsInfo;

    @ApiModelProperty(value = "接口描述", example = "获取用户信息", required = true)
    private String apiDesc;

    /**
     * 权限相关 json
     */
    @ApiModelProperty(value = "权限相关 json", example = "权限相关 json")
    private String authInfo;

    @ApiModelProperty(value = "是否统一加解密 ENC-加密，DENC-解密，ENC_DENC-加密解密")
    private String encryptionRequired;

    @ApiModelProperty(value = "状态 上线中-ONLINE_RUNNING，已上线-ONLINE，下线中-OFFLINE_RUNNING，已下线-OFFLINE_RUNNING")
    private String status;

    @ApiModelProperty(value = "创建人", example = "admin", required = true)
    private String createdBy;

    @ApiModelProperty(value = "更新人", example = "admin")
    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", example = "2025-02-07T14:30:00")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新时间", example = "2025-02-07T14:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "数据版本，每次更新加1", example = "1", required = true)
    private Long version;

    public String getEncryptionRequiredDesc() {
        return EncryptionRequiredEnum.getDescByName(encryptionRequired);
    }

    public String getStatusDesc() {
        return InterfaceStatusEnum.getDescByName(status);
    }

    public List<String> getApiWayList() {
        return StringUtils.isEmpty(apiWay) ? Collections.emptyList() : JSONArray.parseArray(apiWay).toJavaList(String.class);
    }

    public List<String> getApiPathList() {
        return StringUtils.isEmpty(apiPath) ? Collections.emptyList() : JSONArray.parseArray(apiPath).toJavaList(String.class);
    }

    public List<AuthInfoDTO> getAuthInfoList() {
        return StringUtils.isEmpty(authInfo) ? Collections.emptyList() : JSONArray.parseArray(authInfo, AuthInfoDTO.class);
    }
}
