package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceSimpleReq;
import com.yxt.safecenter.common.model.dto.req.SetAuthInfoReq;
import com.yxt.safecenter.service.manager.iface.SafeInterfaceManager;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/b/manual")
@Api(tags = "手动接口")
@Slf4j
public class ManualController extends AbstractController {

    @Resource
    private SafeInterfaceManager safeInterfaceManager;

    @ApiOperation(value = "删除无用接口", notes = "删除无用接口")
    @PostMapping(value = "/deleteByApplicationNameAndClass")
    public ResponseBase<String> deleteByApplicationNameAndClass(@RequestBody @Validated SafeInterfaceSimpleReq req) {
        int i = safeInterfaceManager.deleteByApplicationNameAndClass(req.getApplicationName(), req.getApiClassList());
        return generateSuccess(String.valueOf(i));
    }

    @PostMapping(value = "/transform")
    public ResponseBase<Map> transform(@RequestHeader String headerTest, @RequestHeader(required = false) String headerTest1, String scene, String scene1, @RequestBody Map<String, Object> map) {

        System.out.println("开始处理参数");
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("id", 24242342);
        return generateSuccess(stringObjectHashMap);
    }
}
